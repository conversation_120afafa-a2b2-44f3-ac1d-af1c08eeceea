* test pages to populate
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/download/george-orwell-1984_202309 pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/download/AdolfHitlerMeinKampfEnglishUnexpurgated1939_201904 pdf,epub

# banned books
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/wonderfulwizardo00baumiala pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/adventuresoftoms00twaiiala pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/callofthewild00lond pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/warofworlds00welluoft pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/adventuresofhuck00twai pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/plainliteraltran10burtuoft pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/wealthofnations00smituoft pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/holybiblecontain00philuoft pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/candide00volt_1 pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/madamebovary00flau_5 pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/fairytalesofbrot00grim pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/beautifuldamned00fitzrich pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/tarzanofapes00burruoft pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/ulyssesshake1922_1large pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/blackbeauty00seweiala pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/bostoniansnovel00jamerich pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/6edoriginspecies00darwuoft pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/pointcounterpoin0000aldo_k3u2 pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/jungle00sinc pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/the-communist-manifesto_202507 pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/details/thestateandrevolutionbyv.i.lenin/mode/2up pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/download/in.ernet.dli.2015.238114 pdf,epub
cargo run --bin ia_downloader -- "genesis pod" https://archive.org/download/socialismutopian39257gut pdf,epub


* High level description
  - want a fast an easy way to bulk download items from the Internet Archive and upload them to Autonomi and add relevant metadata using colony
  - minimal inputs required from the user. Must be as automated as possible
  - command line interface should be well documented and interactive using the same libraries as used in colonyd.rs and colony.rs
  - should leverage the colonyd REST API to handle colony metadata transactions
* Program details
  - Broken into 2 separate rust programs: ia_downloader.rs and colony_uploader.rs
    - create both of these in the src/bin directory
  - For now, just do the ia_downloader.rs, the colony_uploader.rs will come next
** ia_downloader.rs
*** arguments
    - pod name or address to record meta data in, for example: "genesis pod" or "12345bcdabcdef123467890a5678ef123456789090abcdef7890abcdef123456"
      - the pod must already have been created by the colony program for it to be found
    - html link where the content lives on the internet archive, for example: https://archive.org/details/george-orwell-1984_202309
    - comma separated list of file extensions to get, for example: "pdf,txt,epub"
    - optional uploader directory, default to colony_uploader and create this directory in the PWD if it doesn't exist and the argument is not specified
*** functionality
    - For the link given, create a new sub-directory in the uploader directory with the name of the internet archive object being downloaded
      - For example, the given link: https://archive.org/details/george-orwell-1984_202309 would have a sub-directory called "george-orwell-1984_202309"
    - For the link given, download the metadata file  in the newly created sub-directory.
      - For the example link above, it looks like this: https://archive.org/download/george-orwell-1984_202309/george-orwell-1984_202309_meta.xml
    - For the link given, download the files list XML file in the newly created sub-directory
      - For the example link above, it looks like this: https://archive.org/download/george-orwell-1984_202309/george-orwell-1984_202309_files.xml
    - Parse the files list XML file for the file names with the extensions listed
      - The XML file will have a "file name" identifier. To find the 'pdf' file extension file, the XML line would look
        something like this: <file name="George Orwell - 1984.pdf" source="original">
        - only the string after the `<file_name=` is what should be checked for the file extension
      - if a file with the matching extension is not found, throw a warning and gracefully continue
    - For each extension found, create sub-directories in the object directory using the suffix as the directory name
      - For example, for the 'pdf' extension, the path would be "george-orwell-1984_202309/pdf"
    - Download each of the files found in the XML based on the requested file extensions into their respective extension directory
      - Multiple files exist on this page. Download the files that match the file extensions in the file extension list. For example:
        - https://archive.org/download/george-orwell-1984_202309/George%20Orwell%20-%201984.pdf would download into "george-orwell-1984_202309/pdf"
        - https://archive.org/download/george-orwell-1984_202309/George%20Orwell%20-%201984_djvu.txt would download into "george-orwell-1984_202309/txt"
        - https://archive.org/download/george-orwell-1984_202309/George%20Orwell%20-%201984.epub would download into "george-orwell-1984_202309/epub"
    - For each downloaded file, run the Autonomi command to get the address where it will exist on the public network
      - See the 'data_put_public()' function in the Autonomi APIs here: https://github.com/maidsafe/autonomi/blob/main/autonomi/src/client/high_level/data/public.rs#L38
        This function returns the data address. Should be able to use the same calculation this code uses to get the map_xor_name value
    - Next write the JSON-LD description from the metadata file and data that can come from the file itself
      - Write the JSON-LD formatted file into a new file call metadata.json and place it into the respective extension directory
      - The schema used for the JSON-LD comes from schema.org
        - The "schema:name" is the name of the file
        - The "schema:author" is the "creator" from the XML file
        - The "schema:contentSize" is the size of the file in bytes
        - The "schema:encodingFormat" is IANA listed format based on the file extension
          - is there a way to list all of these or do a lookup? Unsure the best way to query for these and apply the right value from the file extension.
        - The "schema:description" is the "description" text from the XML file
        - The "@type" is "schema:Book" because the XML lists this as an 'ebook'
          - This mapping is unique for books. This should be some kind of a list that is easy to add to as we add additional type mappings in the future.
        - The "@id" is the Autonomi address calculated in the previous step prefixed by "ant://"
      - As an example, the JSON-LD file should look like this:
        ```
{
  "@context": {"schema": "http://schema.org/"},
  "@type": "schema:Book",
  "@id": "ant://dcb90722cd6c7a3c66527fd8401970cad21cfc61f17e37abd421414ca26900f6",
  "schema:name": "George Orwell - 1984.pdf"
  "schema:description": "1984 is a dystopian social science fiction novel and cautionary tale by English writer George Orwell. It was published on 8 June 1949 by Secker &amp; Warburg as Orwell's ninth and final book completed in his lifetime. Thematically, it centres on the consequences of totalitarianism, mass surveillance and repressive regimentation of people and behaviours within society. Orwell, a democratic socialist, modelled the authoritarian state in the novel on the Soviet Union in the era of Stalinism, and Nazi Germany. More broadly, the novel examines the role of truth and facts within societies and the ways in which they can be manipulated.<br /><br />The story takes place in an imagined future in an unknown year believed to be 1984, when much of the world is in perpetual war. Great Britain, now known as Airstrip One, has become a province of the totalitarian superstate Oceania, which is led by Big Brother, a dictatorial leader supported by an intense cult of personality manufactured by the Party's Thought Police. Through the Ministry of Truth, the Party engages in omnipresent government surveillance, historical negationism, and constant propaganda to persecute individuality and independent thinking.<br /><br />The protagonist, Winston Smith, is a diligent mid-level worker at the Ministry of Truth who secretly hates the Party and dreams of rebellion. Smith keeps a forbidden diary. He begins a relationship with a colleague, Julia, and they learn about a shadowy resistance group called the Brotherhood. However, their contact within the Brotherhood turns out to be a Party agent, and Smith and Julia are arrested. He is subjected to months of psychological manipulation and torture by the Ministry of Love and is released once he has come to love Big Brother.<br /><br />Nineteen Eighty-Four has become a classic literary example of political and dystopian fiction. It also popularised the term "Orwellian" as an adjective, with many terms used in the novel entering common usage, including "Big Brother", "doublethink", "Thought Police", "thoughtcrime", "Newspeak", and "2 + 2 = 5". Parallels have been drawn between the novel's subject matter and real life instances of totalitarianism, mass surveillance, and violations of freedom of expression among other themes. Orwell described his book as a "satire", and a display of the "perversions to which a centralised economy is liable," while also stating he believed "that something resembling it could arrive." Time included the novel on its list of the 100 best English-language novels which were published from 1923 to 2005, and it was placed on the Modern Library's 100 Best Novels list, reaching number 13 on the editors' list and number 6 on the readers' list. In 2003, it was listed at number eight on The Big Read survey by the BBC",
  "schema:author": "George Orwell",
  "schema:contentSize": "1624768",
  "schema:encodingFormat": "application/pdf"
}
        ```
        when the '*_meta.xml' downloaded from the internet archive looks like this:
        ```
<metadata>
<script/>
<identifier>george-orwell-1984_202309</identifier>
<mediatype>texts</mediatype>
<collection>opensource</collection>
<creator>George Orwell</creator>
<date>1949-06-08</date>
<description><b><u><i>1984</i></u></b> is a dystopian social science fiction novel and cautionary tale by English writer George Orwell. It was published on 8 June 1949 by Secker &amp; Warburg as Orwell's ninth and final book completed in his lifetime. Thematically, it centres on the consequences of totalitarianism, mass surveillance and repressive regimentation of people and behaviours within society. Orwell, a democratic socialist, modelled the authoritarian state in the novel on the Soviet Union in the era of Stalinism, and Nazi Germany. More broadly, the novel examines the role of truth and facts within societies and the ways in which they can be manipulated.<br /><br />The story takes place in an imagined future in an unknown year believed to be 1984, when much of the world is in perpetual war. Great Britain, now known as Airstrip One, has become a province of the totalitarian superstate Oceania, which is led by Big Brother, a dictatorial leader supported by an intense cult of personality manufactured by the Party's Thought Police. Through the Ministry of Truth, the Party engages in omnipresent government surveillance, historical negationism, and constant propaganda to persecute individuality and independent thinking.<br /><br />The protagonist, Winston Smith, is a diligent mid-level worker at the Ministry of Truth who secretly hates the Party and dreams of rebellion. Smith keeps a forbidden diary. He begins a relationship with a colleague, Julia, and they learn about a shadowy resistance group called the Brotherhood. However, their contact within the Brotherhood turns out to be a Party agent, and Smith and Julia are arrested. He is subjected to months of psychological manipulation and torture by the Ministry of Love and is released once he has come to love Big Brother.<br /><br />Nineteen Eighty-Four has become a classic literary example of political and dystopian fiction. It also popularised the term "Orwellian" as an adjective, with many terms used in the novel entering common usage, including "Big Brother", "doublethink", "Thought Police", "thoughtcrime", "Newspeak", and "2 + 2 = 5". Parallels have been drawn between the novel's subject matter and real life instances of totalitarianism, mass surveillance, and violations of freedom of expression among other themes. Orwell described his book as a "satire", and a display of the "perversions to which a centralised economy is liable," while also stating he believed "that something resembling it could arrive." Time included the novel on its list of the 100 best English-language novels which were published from 1923 to 2005, and it was placed on the Modern Library's 100 Best Novels list, reaching number 13 on the editors' list and number 6 on the readers' list. In 2003, it was listed at number eight on The Big Read survey by the BBC.<br /></description>
<language>eng</language>
<scanner>Internet Archive HTML5 Uploader 1.7.0</scanner>
<subject>ebook</subject>
<subject>pdf</subject>
<subject>orwell</subject>
<subject>1984</subject>
<title>1984</title>
<uploader><EMAIL></uploader>
<publicdate>2023-09-03 17:40:03</publicdate>
<addeddate>2023-09-03 17:40:03</addeddate>
<curation>[curator]<EMAIL>[/curator][date]20230903174529[/date][comment]checked for malware[/comment]</curation>
<identifier-access>http://archive.org/details/george-orwell-1984_202309</identifier-access>
<identifier-ark>ark:/13960/s2qk0zmwcfj</identifier-ark>
<ppi>300</ppi>
<ocr>tesseract 5.3.0-3-g9920</ocr>
<ocr_parameters>-l eng</ocr_parameters>
<ocr_module_version>0.0.21</ocr_module_version>
<ocr_detected_script>Latin</ocr_detected_script>
<ocr_detected_script_conf>1.0000</ocr_detected_script_conf>
<ocr_detected_lang>en</ocr_detected_lang>
<ocr_detected_lang_conf>1.0000</ocr_detected_lang_conf>
<page_number_confidence>100</page_number_confidence>
<page_number_module_version>1.0.3</page_number_module_version>
</metadata>
        ```
        - Note that the mapping between XML and JSON-LD should be easy to add to as this is just one file type. Later we'll add videos, images, audio, etc.
    - Use the indicatif library to indicate status
    - Use icons like the colony.rs program to make the application visually appealing
** colony_uploader.rs
   - NOTE: stop here, will do this next
   - gets colonyd token and has a WDT thread the automatically updates the token every 9 minutes
   - walks through each uploader directory
   - runs each shell script
     - several in parallel would be good
   - keep track of success and failure
     - print them out as the script progresses
     - on success delete the working directory and content
   - when done, upload the metadata to colony
