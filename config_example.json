{"_comment": "Example configuration file for ia_downloader", "_comment2": "Copy this to ~/.config/ia_downloader/config.json and customize", "huggingface_api_key": "your_huggingface_api_key_here", "tmdb_api_key": null, "_ai_models_comment": "Popular AI models for text summarization:", "_ai_models_options": ["https://api-inference.huggingface.co/models/facebook/bart-large-cnn", "https://api-inference.huggingface.co/models/google/pegasus-xsum", "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium", "https://api-inference.huggingface.co/models/t5-base", "https://api-inference.huggingface.co/models/sshleifer/distilbart-cnn-12-6"], "ai_model_url": "https://api-inference.huggingface.co/models/facebook/bart-large-cnn", "enable_ai_enhancement": false, "default_output_dir": "colony_uploader", "max_concurrent_downloads": 3, "_usage_notes": ["Set enable_ai_enhancement to true to enable AI-powered description enhancement", "Get your free Hugging Face API key from: https://huggingface.co/settings/tokens", "Different AI models may produce different quality summaries", "BART models are generally good for summarization", "Pegasus models are optimized for news summarization", "T5 models are versatile but may need different prompting"]}