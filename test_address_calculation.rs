use autonomi::{client::data::DataAddress, self_encryption::encrypt};
use bytes::Bytes;
use std::fs;
use hex;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let file_path = "test_files/<PERSON> - 1984.pdf";
    let file_content = fs::read(file_path)?;
    let file_size = file_content.len();

    // Use Autonomi's proper address calculation
    // This follows the same process as data_put_public: encrypt the data and get the data map chunk address
    let bytes = Bytes::from(file_content);
    let (data_map_chunk, _chunks) = encrypt(bytes)?;
    let map_xor_name = *data_map_chunk.address().xorname();
    let data_address = DataAddress::new(map_xor_name);
    let address = hex::encode(data_address.xorname().0);

    println!("File: {}", file_path);
    println!("Size: {} bytes", file_size);
    println!("Autonomi Address: {}", address);
    println!("Expected Address: 48bd835e4eef2631c66a840904a5cc114ca2403f2aa63b3d00aa789f6f1f2632");
    
    if address == "48bd835e4eef2631c66a840904a5cc114ca2403f2aa63b3d00aa789f6f1f2632" {
        println!("✅ Address calculation is CORRECT!");
    } else {
        println!("❌ Address calculation is INCORRECT!");
        println!("   Got:      {}", address);
        println!("   Expected: 48bd835e4eef2631c66a840904a5cc114ca2403f2aa63b3d00aa789f6f1f2632");
    }
    
    Ok(())
}
